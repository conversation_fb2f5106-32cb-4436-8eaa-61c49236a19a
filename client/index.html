<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="<PERSON><PERSON><PERSON> - Full-Stack Developer Portfolio. Showcasing innovative web applications and modern development skills." />
    <meta name="keywords" content="Full-Stack Developer, React, Node.js, JavaScript, Web Development, Portfolio" />
    <meta name="author" content="<PERSON><PERSON><PERSON>" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<PERSON><PERSON><PERSON> - Full-Stack Developer" />
    <meta property="og:description" content="Full-Stack Developer passionate about creating innovative web solutions and exceptional user experiences." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://abhaysinghtomar.dev" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="<PERSON><PERSON><PERSON> - Full-Stack Developer" />
    <meta name="twitter:description" content="Full-Stack Developer passionate about creating innovative web solutions and exceptional user experiences." />

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: {
              // Apple's color palette
              apple: {
                blue: '#007AFF',
                'blue-dark': '#0051D5',
                gray: {
                  50: '#FAFAFA',
                  100: '#F5F5F7',
                  200: '#E8E8ED',
                  300: '#D2D2D7',
                  400: '#86868B',
                  500: '#515154',
                  600: '#424245',
                  700: '#1D1D1F',
                  800: '#000000',
                }
              },
              // Dark mode colors
              dark: {
                bg: '#000000',
                surface: '#1D1D1F',
                card: '#2C2C2E',
                text: '#F5F5F7',
                'text-secondary': '#86868B',
              }
            },
            fontFamily: {
              'sf-pro': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', 'sans-serif'],
              sans: ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', 'sans-serif'],
            },
            fontSize: {
              'hero': ['4.5rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
              'hero-mobile': ['2.5rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
              'display': ['3rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }],
              'title': ['2rem', { lineHeight: '1.3', letterSpacing: '-0.01em' }],
            },
            spacing: {
              '18': '4.5rem',
              '88': '22rem',
              '128': '32rem',
            },
            backdropBlur: {
              'apple': '20px',
            },
            animation: {
              'fade-in': 'fadeIn 0.6s ease-out',
              'slide-up': 'slideUp 0.8s ease-out',
              'scale-in': 'scaleIn 0.3s ease-out',
              'float': 'float 6s ease-in-out infinite',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0', transform: 'translateY(20px)' },
                '100%': { opacity: '1', transform: 'translateY(0)' },
              },
              slideUp: {
                '0%': { opacity: '0', transform: 'translateY(40px)' },
                '100%': { opacity: '1', transform: 'translateY(0)' },
              },
              scaleIn: {
                '0%': { opacity: '0', transform: 'scale(0.95)' },
                '100%': { opacity: '1', transform: 'scale(1)' },
              },
              float: {
                '0%, 100%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-10px)' },
              },
            },
            boxShadow: {
              'apple': '0 4px 20px rgba(0, 0, 0, 0.1)',
              'apple-lg': '0 10px 40px rgba(0, 0, 0, 0.15)',
              'apple-card': '0 2px 10px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      }
    </script>

    <title>Abhay Singh Tomar - Full-Stack Developer</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
