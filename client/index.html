<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="<PERSON><PERSON><PERSON> - Full-Stack Developer Portfolio. Showcasing innovative web applications and modern development skills." />
    <meta name="keywords" content="Full-Stack Developer, React, Node.js, JavaScript, Web Development, Portfolio" />
    <meta name="author" content="<PERSON><PERSON><PERSON>" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<PERSON><PERSON><PERSON> - Full-Stack Developer" />
    <meta property="og:description" content="Full-Stack Developer passionate about creating innovative web solutions and exceptional user experiences." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://abhaysinghtomar.dev" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="<PERSON><PERSON><PERSON> - Full-Stack Developer" />
    <meta name="twitter:description" content="Full-Stack Developer passionate about creating innovative web solutions and exceptional user experiences." />

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a',
              }
            },
            fontFamily: {
              sans: ['Inter', 'system-ui', 'sans-serif'],
            },
            animation: {
              'fade-in': 'fadeIn 0.5s ease-in-out',
              'slide-up': 'slideUp 0.5s ease-out',
              'bounce-slow': 'bounce 2s infinite',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0' },
                '100%': { opacity: '1' },
              },
              slideUp: {
                '0%': { transform: 'translateY(20px)', opacity: '0' },
                '100%': { transform: 'translateY(0)', opacity: '1' },
              },
            },
          },
        },
      }
    </script>

    <title>Abhay Singh Tomar - Full-Stack Developer</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
