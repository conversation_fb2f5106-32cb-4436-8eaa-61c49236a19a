{"version": 3, "file": "pax.js", "sourceRoot": "", "sources": ["../../src/pax.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAA;AACpC,OAAO,EAAE,MAAM,EAAc,MAAM,aAAa,CAAA;AAEhD,MAAM,OAAO,GAAG;IACd,KAAK,CAAO;IACZ,KAAK,CAAO;IACZ,KAAK,CAAO;IAEZ,OAAO,CAAS;IAChB,OAAO,CAAS;IAEhB,GAAG,CAAS;IACZ,GAAG,CAAS;IAEZ,KAAK,CAAS;IACd,KAAK,CAAS;IACd,QAAQ,CAAS;IACjB,GAAG,CAAS;IACZ,GAAG,CAAS;IACZ,KAAK,CAAS;IACd,IAAI,CAAS;IACb,IAAI,CAAS;IACb,IAAI,CAAS;IAEb,MAAM,CAAS;IAEf,YAAY,GAAe,EAAE,SAAkB,KAAK;QAClD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;QACtB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;QAC1B,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;QACtB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;QAClB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;QAClB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;QACtB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;QAClB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;QAC5B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;QACtB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;QACpB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;QACpB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;QAClB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;IACxB,CAAC;IAED,MAAM;QACJ,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;QAC9B,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YAChB,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAC9B,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACvC,wBAAwB;QACxB,qBAAqB;QACrB,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,GAAG,CAAC,CAAA;QACjD,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAEtC,0DAA0D;QAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACZ,CAAC;QAED,IAAI,MAAM,CAAC;YACT,qBAAqB;YACrB,kEAAkE;YAClE,2BAA2B;YAC3B,qBAAqB;YACrB,IAAI,EAAE,CAAC,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;YAC7D,oBAAoB;YACpB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK;YACxB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,gBAAgB;YAC7D,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;YACvB,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;YACT,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QAEd,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;QAErC,0BAA0B;QAC1B,KAAK,IAAI,CAAC,GAAG,OAAO,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACZ,CAAC;QAED,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,UAAU;QACR,OAAO,CACL,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACxB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;YAC3B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;YAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACxB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAC1B,CAAA;IACH,CAAC;IAED,WAAW,CAAC,KAAgB;QAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;YAC9B,OAAO,EAAE,CAAA;QACX,CAAC;QACD,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;QACrB,MAAM,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACpD,MAAM,CAAC,GACL,GAAG;YACH,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,OAAO,CAAC,CAAC;gBACxD,SAAS;gBACX,CAAC,CAAC,EAAE,CAAC;YACL,KAAK;YACL,GAAG;YACH,CAAC;YACD,IAAI,CAAA;QACN,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QACpC,gEAAgE;QAChE,+DAA+D;QAC/D,2BAA2B;QAC3B,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;QAC7D,IAAI,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,CAAA;QACb,CAAC;QACD,MAAM,GAAG,GAAG,MAAM,GAAG,OAAO,CAAA;QAC5B,OAAO,GAAG,GAAG,CAAC,CAAA;IAChB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,GAAW,EAAE,EAAe,EAAE,IAAa,KAAK;QAC3D,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAC5C,CAAC;CACF;AAED,MAAM,KAAK,GAAG,CAAC,CAAa,EAAE,CAAc,EAAE,EAAE,CAC9C,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAEjC,MAAM,OAAO,GAAG,CAAC,GAAW,EAAE,EAAE,CAC9B,GAAG;KACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;KAClB,KAAK,CAAC,IAAI,CAAC;KACX,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;AAE7C,MAAM,WAAW,GAAG,CAAC,GAAwB,EAAE,IAAY,EAAE,EAAE;IAC7D,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IAE5B,6CAA6C;IAC7C,iDAAiD;IACjD,IAAI,CAAC,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACtC,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,CAAA;IACnC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC1B,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAA;IAEpB,IAAI,CAAC,CAAC,EAAE,CAAC;QACP,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAA;IAErD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACtB,GAAG,CAAC,CAAC,CAAC;QACJ,yCAAyC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAC5B,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC,CAAA;IACL,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA", "sourcesContent": ["import { basename } from 'node:path'\nimport { <PERSON><PERSON>, HeaderData } from './header.js'\n\nexport class Pax implements HeaderData {\n  atime?: Date\n  mtime?: Date\n  ctime?: Date\n\n  charset?: string\n  comment?: string\n\n  gid?: number\n  uid?: number\n\n  gname?: string\n  uname?: string\n  linkpath?: string\n  dev?: number\n  ino?: number\n  nlink?: number\n  path?: string\n  size?: number\n  mode?: number\n\n  global: boolean\n\n  constructor(obj: HeaderData, global: boolean = false) {\n    this.atime = obj.atime\n    this.charset = obj.charset\n    this.comment = obj.comment\n    this.ctime = obj.ctime\n    this.dev = obj.dev\n    this.gid = obj.gid\n    this.global = global\n    this.gname = obj.gname\n    this.ino = obj.ino\n    this.linkpath = obj.linkpath\n    this.mtime = obj.mtime\n    this.nlink = obj.nlink\n    this.path = obj.path\n    this.size = obj.size\n    this.uid = obj.uid\n    this.uname = obj.uname\n  }\n\n  encode() {\n    const body = this.encodeBody()\n    if (body === '') {\n      return Buffer.allocUnsafe(0)\n    }\n\n    const bodyLen = Buffer.byteLength(body)\n    // round up to 512 bytes\n    // add 512 for header\n    const bufLen = 512 * Math.ceil(1 + bodyLen / 512)\n    const buf = Buffer.allocUnsafe(bufLen)\n\n    // 0-fill the header section, it might not hit every field\n    for (let i = 0; i < 512; i++) {\n      buf[i] = 0\n    }\n\n    new Header({\n      // XXX split the path\n      // then the path should be PaxHeader + basename, but less than 99,\n      // prepend with the dirname\n      /* c8 ignore start */\n      path: ('PaxHeader/' + basename(this.path ?? '')).slice(0, 99),\n      /* c8 ignore stop */\n      mode: this.mode || 0o644,\n      uid: this.uid,\n      gid: this.gid,\n      size: bodyLen,\n      mtime: this.mtime,\n      type: this.global ? 'GlobalExtendedHeader' : 'ExtendedHeader',\n      linkpath: '',\n      uname: this.uname || '',\n      gname: this.gname || '',\n      devmaj: 0,\n      devmin: 0,\n      atime: this.atime,\n      ctime: this.ctime,\n    }).encode(buf)\n\n    buf.write(body, 512, bodyLen, 'utf8')\n\n    // null pad after the body\n    for (let i = bodyLen + 512; i < buf.length; i++) {\n      buf[i] = 0\n    }\n\n    return buf\n  }\n\n  encodeBody() {\n    return (\n      this.encodeField('path') +\n      this.encodeField('ctime') +\n      this.encodeField('atime') +\n      this.encodeField('dev') +\n      this.encodeField('ino') +\n      this.encodeField('nlink') +\n      this.encodeField('charset') +\n      this.encodeField('comment') +\n      this.encodeField('gid') +\n      this.encodeField('gname') +\n      this.encodeField('linkpath') +\n      this.encodeField('mtime') +\n      this.encodeField('size') +\n      this.encodeField('uid') +\n      this.encodeField('uname')\n    )\n  }\n\n  encodeField(field: keyof Pax): string {\n    if (this[field] === undefined) {\n      return ''\n    }\n    const r = this[field]\n    const v = r instanceof Date ? r.getTime() / 1000 : r\n    const s =\n      ' ' +\n      (field === 'dev' || field === 'ino' || field === 'nlink' ?\n        'SCHILY.'\n      : '') +\n      field +\n      '=' +\n      v +\n      '\\n'\n    const byteLen = Buffer.byteLength(s)\n    // the digits includes the length of the digits in ascii base-10\n    // so if it's 9 characters, then adding 1 for the 9 makes it 10\n    // which makes it 11 chars.\n    let digits = Math.floor(Math.log(byteLen) / Math.log(10)) + 1\n    if (byteLen + digits >= Math.pow(10, digits)) {\n      digits += 1\n    }\n    const len = digits + byteLen\n    return len + s\n  }\n\n  static parse(str: string, ex?: HeaderData, g: boolean = false) {\n    return new Pax(merge(parseKV(str), ex), g)\n  }\n}\n\nconst merge = (a: HeaderData, b?: HeaderData) =>\n  b ? Object.assign({}, b, a) : a\n\nconst parseKV = (str: string) =>\n  str\n    .replace(/\\n$/, '')\n    .split('\\n')\n    .reduce(parseKVLine, Object.create(null))\n\nconst parseKVLine = (set: Record<string, any>, line: string) => {\n  const n = parseInt(line, 10)\n\n  // XXX Values with \\n in them will fail this.\n  // Refactor to not be a naive line-by-line parse.\n  if (n !== Buffer.byteLength(line) + 1) {\n    return set\n  }\n\n  line = line.slice((n + ' ').length)\n  const kv = line.split('=')\n  const r = kv.shift()\n\n  if (!r) {\n    return set\n  }\n\n  const k = r.replace(/^SCHILY\\.(dev|ino|nlink)/, '$1')\n\n  const v = kv.join('=')\n  set[k] =\n    /^([A-Z]+\\.)?([mac]|birth|creation)time$/.test(k) ?\n      new Date(Number(v) * 1000)\n    : /^[0-9]+$/.test(v) ? +v\n    : v\n  return set\n}\n"]}