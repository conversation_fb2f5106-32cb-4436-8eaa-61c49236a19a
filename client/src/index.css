/* Apple-inspired Base Styles */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  background-color: #ffffff;
  color: #1d1d1f;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode */
.dark body {
  background-color: #000000;
  color: #f5f5f7;
}

/* Apple-style Navigation Blur */
.nav-blur {
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
}

/* Apple Button Styles */
.btn-apple {
  @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full transition-all duration-200 ease-out;
  @apply bg-apple-blue hover:bg-apple-blue-dark text-white;
  @apply focus:outline-none focus:ring-2 focus:ring-apple-blue focus:ring-offset-2;
  @apply transform hover:scale-105 active:scale-95;
}

.btn-apple-secondary {
  @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full transition-all duration-200 ease-out;
  @apply bg-apple-gray-100 hover:bg-apple-gray-200 text-apple-gray-700;
  @apply dark:bg-dark-card dark:hover:bg-apple-gray-600 dark:text-dark-text;
  @apply focus:outline-none focus:ring-2 focus:ring-apple-gray-400 focus:ring-offset-2;
  @apply transform hover:scale-105 active:scale-95;
}

/* Apple Card Styles */
.card-apple {
  @apply bg-white dark:bg-dark-card rounded-2xl shadow-apple transition-all duration-300 ease-out;
  @apply hover:shadow-apple-lg hover:-translate-y-1;
}

/* Apple Section Spacing */
.section-apple {
  @apply py-20 lg:py-32 px-4 sm:px-6 lg:px-8;
}

.container-apple {
  @apply max-w-6xl mx-auto;
}

/* Apple Typography */
.text-hero {
  @apply text-hero-mobile md:text-hero font-bold tracking-tight text-apple-gray-700 dark:text-dark-text;
}

.text-display {
  @apply text-display font-semibold tracking-tight text-apple-gray-700 dark:text-dark-text;
}

.text-title {
  @apply text-title font-semibold tracking-tight text-apple-gray-700 dark:text-dark-text;
}

.text-body {
  @apply text-lg leading-relaxed text-apple-gray-500 dark:text-dark-text-secondary;
}

/* Apple Form Styles */
.input-apple {
  @apply w-full px-4 py-4 text-base bg-apple-gray-50 dark:bg-dark-surface border-0 rounded-xl;
  @apply focus:outline-none focus:ring-2 focus:ring-apple-blue focus:bg-white dark:focus:bg-dark-card;
  @apply transition-all duration-200 ease-out;
  @apply placeholder-apple-gray-400 dark:placeholder-dark-text-secondary;
  @apply text-apple-gray-700 dark:text-dark-text;
}

/* Smooth Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-on-scroll.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Apple-style Grid */
.grid-apple {
  @apply grid gap-8 md:gap-12;
}

/* Parallax Effect */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}
