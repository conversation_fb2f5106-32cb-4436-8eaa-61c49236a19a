/* Apple-inspired Base Styles */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  background-color: #ffffff;
  color: #1d1d1f;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode */
.dark body {
  background-color: #000000;
  color: #f5f5f7;
}

/* Apple-style Navigation Blur */
.nav-blur {
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
}

/* Enhanced Apple Button Styles */
.btn-apple {
  @apply inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-2xl transition-all duration-300 ease-out;
  @apply bg-apple-blue hover:bg-apple-blue-dark text-white;
  @apply focus:outline-none focus:ring-2 focus:ring-apple-blue focus:ring-offset-2;
  @apply transform hover:scale-105 active:scale-95 shadow-apple-lg hover:shadow-apple;
}

.btn-apple-secondary {
  @apply inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-2xl transition-all duration-300 ease-out;
  @apply bg-white dark:bg-dark-card hover:bg-apple-gray-50 dark:hover:bg-apple-gray-600 text-apple-gray-700 dark:text-dark-text;
  @apply border border-apple-gray-200 dark:border-apple-gray-600 hover:border-apple-blue;
  @apply focus:outline-none focus:ring-2 focus:ring-apple-gray-400 focus:ring-offset-2;
  @apply transform hover:scale-105 active:scale-95 shadow-apple hover:shadow-apple-lg;
}

/* Enhanced Apple Card Styles */
.card-apple {
  @apply bg-white dark:bg-dark-card rounded-3xl shadow-apple transition-all duration-500 ease-out;
  @apply hover:shadow-apple-lg hover:-translate-y-2;
  @apply border border-apple-gray-200/30 dark:border-apple-gray-600/20;
}

/* Apple Section Spacing - Improved */
.section-apple {
  @apply py-24 lg:py-40 px-6 sm:px-8 lg:px-12;
}

.section-apple-tight {
  @apply py-16 lg:py-24 px-6 sm:px-8 lg:px-12;
}

.container-apple {
  @apply max-w-7xl mx-auto;
}

.container-apple-narrow {
  @apply max-w-4xl mx-auto;
}

.container-apple-wide {
  @apply max-w-8xl mx-auto;
}

/* Apple Typography - Enhanced */
.text-hero {
  @apply text-hero-mobile md:text-hero font-bold tracking-tight text-apple-gray-700 dark:text-dark-text leading-none;
}

.text-display {
  @apply text-4xl md:text-5xl lg:text-6xl font-semibold tracking-tight text-apple-gray-700 dark:text-dark-text leading-tight;
}

.text-title {
  @apply text-2xl md:text-3xl font-semibold tracking-tight text-apple-gray-700 dark:text-dark-text leading-tight;
}

.text-subtitle {
  @apply text-xl md:text-2xl font-medium tracking-tight text-apple-gray-600 dark:text-dark-text-secondary leading-relaxed;
}

.text-body {
  @apply text-lg md:text-xl leading-relaxed text-apple-gray-500 dark:text-dark-text-secondary;
}

.text-body-sm {
  @apply text-base md:text-lg leading-relaxed text-apple-gray-500 dark:text-dark-text-secondary;
}

/* Enhanced Apple Form Styles */
.input-apple {
  @apply w-full px-6 py-4 text-base bg-apple-gray-50 dark:bg-dark-surface border border-apple-gray-200/50 dark:border-apple-gray-600/20 rounded-2xl;
  @apply focus:outline-none focus:ring-2 focus:ring-apple-blue focus:bg-white dark:focus:bg-dark-card focus:border-apple-blue;
  @apply transition-all duration-300 ease-out;
  @apply placeholder-apple-gray-400 dark:placeholder-dark-text-secondary;
  @apply text-apple-gray-700 dark:text-dark-text;
  @apply shadow-sm focus:shadow-apple;
}

/* Smooth Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-on-scroll.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Apple-style Grid */
.grid-apple {
  @apply grid gap-8 md:gap-12;
}

/* Parallax Effect */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

/* Additional Utility Classes */
.text-balance {
  text-wrap: balance;
}

.backdrop-blur-apple {
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
}

/* Enhanced Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Improved Focus States */
.focus-apple {
  @apply focus:outline-none focus:ring-2 focus:ring-apple-blue focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-dark-bg;
}

/* Gradient Text */
.text-gradient-apple {
  background: linear-gradient(135deg, #007AFF 0%, #0051D5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
