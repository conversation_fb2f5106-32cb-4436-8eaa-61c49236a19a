import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { FiArrowDown, FiGithub, FiLinkedin, FiMail } from 'react-icons/fi';

const Hero = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const scrollToProjects = () => {
    const element = document.getElementById('projects');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section
      id="hero"
      className="relative min-h-screen flex items-center justify-center bg-white dark:bg-dark-bg overflow-hidden"
    >
      {/* Apple-style Background */}
      <div className="absolute inset-0">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-apple-gray-50 via-white to-apple-gray-100 dark:from-dark-bg dark:via-dark-surface dark:to-dark-bg"></div>

        {/* Floating geometric shapes - Apple style */}
        <motion.div
          animate={{
            x: [0, 30, 0],
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-apple-blue/10 to-transparent rounded-full blur-3xl"
          style={{
            transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,
          }}
        />
        <motion.div
          animate={{
            x: [0, -20, 0],
            y: [0, 30, 0],
            rotate: [0, -3, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-tl from-apple-blue/5 to-transparent rounded-full blur-3xl"
          style={{
            transform: `translate(${mousePosition.x * -0.01}px, ${mousePosition.y * -0.01}px)`,
          }}
        />
      </div>

      <div className="relative z-10 container-apple section-apple text-center">
        <motion.div
          initial={{ opacity: 0, y: 60 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1.2, ease: "easeOut" }}
          className="max-w-5xl mx-auto"
        >
          {/* Greeting - Apple style subtle intro */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-lg md:text-xl font-medium mb-6 text-apple-gray-500 dark:text-dark-text-secondary tracking-wide"
          >
            Hi, I'm
          </motion.p>

          {/* Name - Apple's bold, impactful typography */}
          <motion.h1
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="text-hero font-bold mb-8 leading-none tracking-tight"
          >
            Abhay Singh Tomar
          </motion.h1>

          {/* Title - Clean, Apple-style subtitle */}
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="text-display mb-12 text-apple-gray-600 dark:text-dark-text-secondary font-light"
          >
            Full-Stack Developer
          </motion.h2>

          {/* Description - Apple's clean, readable body text */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="text-body mb-16 max-w-3xl mx-auto"
          >
            I create exceptional digital experiences through innovative web applications.
            Passionate about clean code, user experience, and bringing ideas to life with
            modern technologies.
          </motion.p>

          {/* CTA Buttons - Apple style */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.1 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mb-20"
          >
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={scrollToProjects}
              className="btn-apple"
            >
              View My Work
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
              className="btn-apple-secondary"
            >
              Get In Touch
            </motion.button>
          </motion.div>

          {/* Social Links - Minimalist Apple style */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.3 }}
            className="flex justify-center space-x-8 mb-20"
          >
            <motion.a
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              href="https://github.com/Abhay1930"
              target="_blank"
              rel="noopener noreferrer"
              className="p-3 rounded-full bg-apple-gray-100 dark:bg-dark-surface text-apple-gray-600 dark:text-dark-text-secondary hover:text-apple-blue hover:bg-apple-gray-200 dark:hover:bg-dark-card transition-all duration-200"
              aria-label="GitHub"
            >
              <FiGithub size={20} />
            </motion.a>
            <motion.a
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              href="https://linkedin.com/in/abhay-singh-tomar"
              target="_blank"
              rel="noopener noreferrer"
              className="p-3 rounded-full bg-apple-gray-100 dark:bg-dark-surface text-apple-gray-600 dark:text-dark-text-secondary hover:text-apple-blue hover:bg-apple-gray-200 dark:hover:bg-dark-card transition-all duration-200"
              aria-label="LinkedIn"
            >
              <FiLinkedin size={20} />
            </motion.a>
            <motion.a
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              href="mailto:<EMAIL>"
              className="p-3 rounded-full bg-apple-gray-100 dark:bg-dark-surface text-apple-gray-600 dark:text-dark-text-secondary hover:text-apple-blue hover:bg-apple-gray-200 dark:hover:bg-dark-card transition-all duration-200"
              aria-label="Email"
            >
              <FiMail size={20} />
            </motion.a>
          </motion.div>
        </motion.div>

        {/* Apple-style Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.5 }}
          className="absolute bottom-12 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center text-apple-gray-400 dark:text-dark-text-secondary"
          >
            <span className="text-sm mb-3 font-medium tracking-wide">Scroll to explore</span>
            <div className="w-6 h-10 border-2 border-current rounded-full flex justify-center">
              <motion.div
                animate={{ y: [0, 12, 0] }}
                transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut" }}
                className="w-1 h-3 bg-current rounded-full mt-2"
              />
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
