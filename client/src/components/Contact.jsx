import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiMail, FiPhone, FiMapPin, FiSend, FiCheck, FiX } from 'react-icons/fi';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null); // 'success', 'error', or null

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      const response = await fetch('http://localhost:3001/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({ name: '', email: '', message: '' });
      } else {
        setSubmitStatus('error');
        console.error('Form submission error:', data);
      }
    } catch (error) {
      setSubmitStatus('error');
      console.error('Network error:', error);
    } finally {
      setIsSubmitting(false);
      // Clear status after 5 seconds
      setTimeout(() => setSubmitStatus(null), 5000);
    }
  };

  const contactInfo = [
    {
      icon: <FiMail className="w-6 h-6" />,
      title: 'Email',
      value: '<EMAIL>',
      link: 'mailto:<EMAIL>'
    },
    {
      icon: <FiPhone className="w-6 h-6" />,
      title: 'Phone',
      value: '+****************',
      link: 'tel:+15551234567'
    },
    {
      icon: <FiMapPin className="w-6 h-6" />,
      title: 'Location',
      value: 'New Delhi, India',
      link: null
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="contact" className="section-apple bg-apple-gray-50 dark:bg-dark-bg">
      <div className="container-apple">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={containerVariants}
        >
          {/* Apple-style Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-20">
            <h2 className="text-display mb-6">
              Get In Touch
            </h2>
            <p className="text-body max-w-2xl mx-auto">
              Have a project in mind or want to collaborate? I'd love to hear from you.
              Let's create something amazing together!
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-16 lg:gap-20">
            {/* Enhanced Contact Information */}
            <motion.div variants={itemVariants} className="space-y-10">
              <div>
                <h3 className="text-title mb-8 text-apple-gray-700 dark:text-dark-text">
                  Let's Connect
                </h3>
                <p className="text-body-sm mb-10 leading-relaxed">
                  I'm always open to discussing new opportunities, creative projects,
                  or potential collaborations. Whether you have a question or just want
                  to say hello, feel free to reach out!
                </p>
              </div>

              <div className="space-y-6">
                {contactInfo.map((info) => (
                  <motion.div
                    key={info.title}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="flex items-center gap-6 p-6 card-apple"
                  >
                    <div className="p-4 bg-apple-blue/10 text-apple-blue rounded-2xl">
                      {info.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-apple-gray-700 dark:text-dark-text text-lg mb-1">{info.title}</h4>
                      {info.link ? (
                        <a
                          href={info.link}
                          className="text-apple-gray-500 dark:text-dark-text-secondary hover:text-apple-blue transition-colors duration-300 text-base"
                        >
                          {info.value}
                        </a>
                      ) : (
                        <span className="text-apple-gray-500 dark:text-dark-text-secondary text-base">{info.value}</span>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Enhanced Contact Form */}
            <motion.div variants={itemVariants}>
              <form onSubmit={handleSubmit} className="card-apple p-10 lg:p-12">
                <h3 className="text-title mb-8 text-apple-gray-700 dark:text-dark-text">
                  Send Message
                </h3>

                {/* Status Messages */}
                {submitStatus === 'success' && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-6 p-4 bg-green-100 border border-green-200 text-green-700 rounded-lg flex items-center gap-2"
                  >
                    <FiCheck className="w-5 h-5" />
                    Thank you! Your message has been sent successfully.
                  </motion.div>
                )}

                {submitStatus === 'error' && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-6 p-4 bg-red-100 border border-red-200 text-red-700 rounded-lg flex items-center gap-2"
                  >
                    <FiX className="w-5 h-5" />
                    Sorry, there was an error sending your message. Please try again.
                  </motion.div>
                )}

                <div className="space-y-8">
                  {/* Enhanced Name Field */}
                  <div>
                    <label htmlFor="name" className="block text-base font-medium text-apple-gray-700 dark:text-dark-text mb-3">
                      Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="input-apple text-lg py-5"
                      placeholder="Your full name"
                    />
                  </div>

                  {/* Enhanced Email Field */}
                  <div>
                    <label htmlFor="email" className="block text-base font-medium text-apple-gray-700 dark:text-dark-text mb-3">
                      Email *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="input-apple text-lg py-5"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  {/* Enhanced Message Field */}
                  <div>
                    <label htmlFor="message" className="block text-base font-medium text-apple-gray-700 dark:text-dark-text mb-3">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={6}
                      className="input-apple resize-none text-lg py-5"
                      placeholder="Tell me about your project or just say hello..."
                    />
                  </div>

                  {/* Submit Button */}
                  <motion.button
                    type="submit"
                    disabled={isSubmitting}
                    whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                    whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
                    className={`w-full py-4 px-6 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                      isSubmitting
                        ? 'bg-apple-gray-400 cursor-not-allowed'
                        : 'bg-apple-blue hover:bg-apple-blue-dark focus:ring-2 focus:ring-apple-blue focus:ring-offset-2 shadow-apple'
                    } text-white`}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <FiSend className="w-5 h-5" />
                        Send Message
                      </>
                    )}
                  </motion.button>
                </div>
              </form>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
