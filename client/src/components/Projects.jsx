import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiExternalLink, FiGithub, FiFilter } from 'react-icons/fi';

const Projects = () => {
  const [activeFilter, setActiveFilter] = useState('All');

  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A full-stack e-commerce solution with user authentication, payment integration, and admin dashboard.',
      image: '/api/placeholder/400/250',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/ecommerce-platform'
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates and team collaboration features.',
      image: '/api/placeholder/400/250',
      technologies: ['React', 'Firebase', 'Material-UI', 'Socket.io'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/task-manager'
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'A responsive weather application with location-based forecasts and interactive charts.',
      image: '/api/placeholder/400/250',
      technologies: ['React', 'Chart.js', 'OpenWeather API', 'Tailwind CSS'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/weather-dashboard'
    },
    {
      id: 4,
      title: 'Mobile Fitness Tracker',
      description: 'A React Native mobile app for tracking workouts, nutrition, and fitness goals.',
      image: '/api/placeholder/400/250',
      technologies: ['React Native', 'Expo', 'AsyncStorage', 'Charts'],
      category: 'Mobile',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/fitness-tracker'
    },
    {
      id: 5,
      title: 'Blog CMS',
      description: 'A content management system for bloggers with markdown support and SEO optimization.',
      image: '/api/placeholder/400/250',
      technologies: ['Next.js', 'Prisma', 'PostgreSQL', 'Vercel'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/blog-cms'
    },
    {
      id: 6,
      title: 'Real Estate Platform',
      description: 'A property listing platform with advanced search filters and virtual tour integration.',
      image: '/api/placeholder/400/250',
      technologies: ['Vue.js', 'Express.js', 'MySQL', 'Google Maps API'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/real-estate-platform'
    }
  ];

  const categories = ['All', 'Web', 'Mobile'];

  const filteredProjects = activeFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="projects" className="section-apple bg-apple-gray-50 dark:bg-dark-bg">
      <div className="container-apple">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={containerVariants}
        >
          {/* Enhanced Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-24">
            <h2 className="text-display mb-8">
              Featured Projects
            </h2>
            <p className="text-body-sm max-w-3xl mx-auto">
              Here are some of my recent projects that showcase my skills and passion for development.
            </p>
          </motion.div>

          {/* Enhanced Filter Buttons */}
          <motion.div variants={itemVariants} className="flex justify-center mb-20">
            <div className="flex flex-wrap gap-3 p-3 bg-white dark:bg-dark-surface rounded-2xl shadow-apple border border-apple-gray-200/50 dark:border-apple-gray-600/20">
              {categories.map((category) => (
                <motion.button
                  key={category}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setActiveFilter(category)}
                  className={`px-8 py-4 rounded-xl font-medium transition-all duration-300 text-sm ${
                    activeFilter === category
                      ? 'bg-apple-blue text-white shadow-apple-lg'
                      : 'text-apple-gray-600 dark:text-dark-text-secondary hover:text-apple-blue hover:bg-apple-gray-50 dark:hover:bg-dark-card'
                  }`}
                >
                  {category}
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Enhanced Projects Grid - Fixed Spacing */}
          <div className="w-full">
            <motion.div
              layout
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10 xl:gap-12"
            >
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  layout
                  variants={itemVariants}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -12, scale: 1.03 }}
                  className="card-apple overflow-hidden group cursor-pointer w-full"
                >
                {/* Enhanced Project Image */}
                <div className="relative overflow-hidden">
                  <div className="w-full h-64 bg-gradient-to-br from-apple-blue/15 via-apple-blue/8 to-apple-gray-50 dark:from-apple-blue/10 dark:via-dark-surface dark:to-dark-card flex items-center justify-center group-hover:scale-105 transition-transform duration-700 ease-out">
                    <div className="text-apple-blue text-4xl font-bold tracking-wider opacity-80">
                      {project.title.split(' ').map(word => word[0]).join('')}
                    </div>
                  </div>
                  {/* Replace with actual image */}
                  {/* <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-700 ease-out"
                  /> */}

                  {/* Enhanced Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out flex items-center justify-center gap-4">
                    <motion.a
                      whileHover={{ scale: 1.15, y: -3 }}
                      whileTap={{ scale: 0.9 }}
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-4 bg-white/95 backdrop-blur-md text-apple-gray-700 rounded-2xl hover:bg-apple-blue hover:text-white transition-all duration-300 shadow-apple-lg"
                      aria-label="View Project"
                    >
                      <FiExternalLink className="w-6 h-6" />
                    </motion.a>
                    <motion.a
                      whileHover={{ scale: 1.15, y: -3 }}
                      whileTap={{ scale: 0.9 }}
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-4 bg-white/95 backdrop-blur-md text-apple-gray-700 rounded-2xl hover:bg-apple-blue hover:text-white transition-all duration-300 shadow-apple-lg"
                      aria-label="View Code"
                    >
                      <FiGithub className="w-6 h-6" />
                    </motion.a>
                  </div>
                </div>

                {/* Enhanced Project Content */}
                <div className="p-8 lg:p-10">
                  <h3 className="text-xl lg:text-2xl font-semibold mb-4 text-apple-gray-700 dark:text-dark-text leading-tight">
                    {project.title}
                  </h3>
                  <p className="text-apple-gray-500 dark:text-dark-text-secondary mb-8 leading-relaxed text-base lg:text-lg">
                    {project.description}
                  </p>

                  {/* Enhanced Technologies */}
                  <div className="flex flex-wrap gap-3 mb-10">
                    {project.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-4 py-2 bg-apple-gray-100 dark:bg-dark-surface text-apple-gray-600 dark:text-dark-text-secondary text-sm rounded-full font-medium border border-apple-gray-200/50 dark:border-apple-gray-600/20"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>

                  {/* Enhanced Action Buttons */}
                  <div className="flex gap-4">
                    <motion.a
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 text-center py-4 px-6 bg-apple-blue text-white rounded-xl hover:bg-apple-blue-dark transition-all duration-300 text-base font-medium shadow-apple-lg"
                    >
                      View Project
                    </motion.a>
                    <motion.a
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-4 bg-apple-gray-100 dark:bg-dark-surface text-apple-gray-600 dark:text-dark-text-secondary rounded-xl hover:bg-apple-gray-200 dark:hover:bg-dark-card hover:text-apple-blue transition-all duration-300 border border-apple-gray-200/50 dark:border-apple-gray-600/20"
                    >
                      <FiGithub className="w-6 h-6" />
                    </motion.a>
                  </div>
                </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
