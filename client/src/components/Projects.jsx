import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiExternalLink, FiGithub, FiFilter } from 'react-icons/fi';

const Projects = () => {
  const [activeFilter, setActiveFilter] = useState('All');

  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A full-stack e-commerce solution with user authentication, payment integration, and admin dashboard.',
      image: '/api/placeholder/400/250',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/ecommerce-platform'
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates and team collaboration features.',
      image: '/api/placeholder/400/250',
      technologies: ['React', 'Firebase', 'Material-UI', 'Socket.io'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/task-manager'
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'A responsive weather application with location-based forecasts and interactive charts.',
      image: '/api/placeholder/400/250',
      technologies: ['React', 'Chart.js', 'OpenWeather API', 'Tailwind CSS'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/weather-dashboard'
    },
    {
      id: 4,
      title: 'Mobile Fitness Tracker',
      description: 'A React Native mobile app for tracking workouts, nutrition, and fitness goals.',
      image: '/api/placeholder/400/250',
      technologies: ['React Native', 'Expo', 'AsyncStorage', 'Charts'],
      category: 'Mobile',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/fitness-tracker'
    },
    {
      id: 5,
      title: 'Blog CMS',
      description: 'A content management system for bloggers with markdown support and SEO optimization.',
      image: '/api/placeholder/400/250',
      technologies: ['Next.js', 'Prisma', 'PostgreSQL', 'Vercel'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/blog-cms'
    },
    {
      id: 6,
      title: 'Real Estate Platform',
      description: 'A property listing platform with advanced search filters and virtual tour integration.',
      image: '/api/placeholder/400/250',
      technologies: ['Vue.js', 'Express.js', 'MySQL', 'Google Maps API'],
      category: 'Web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/Abhay1930/real-estate-platform'
    }
  ];

  const categories = ['All', 'Web', 'Mobile'];

  const filteredProjects = activeFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="projects" className="section-apple bg-apple-gray-50 dark:bg-dark-bg">
      <div className="container-apple">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={containerVariants}
        >
          {/* Apple-style Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-20">
            <h2 className="text-display mb-6">
              Featured Projects
            </h2>
            <p className="text-body max-w-2xl mx-auto">
              Here are some of my recent projects that showcase my skills and passion for development.
            </p>
          </motion.div>

          {/* Apple-style Filter Buttons */}
          <motion.div variants={itemVariants} className="flex justify-center mb-16">
            <div className="flex flex-wrap gap-2 p-2 bg-apple-gray-100 dark:bg-dark-surface rounded-2xl">
              {categories.map((category) => (
                <motion.button
                  key={category}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setActiveFilter(category)}
                  className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                    activeFilter === category
                      ? 'bg-apple-blue text-white shadow-apple'
                      : 'text-apple-gray-600 dark:text-dark-text-secondary hover:text-apple-blue hover:bg-white dark:hover:bg-dark-card'
                  }`}
                >
                  {category}
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Apple-style Projects Grid */}
          <motion.div
            layout
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12"
          >
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                layout
                variants={itemVariants}
                whileHover={{ y: -8, scale: 1.02 }}
                className="card-apple overflow-hidden group cursor-pointer"
              >
                {/* Apple-style Project Image */}
                <div className="relative overflow-hidden rounded-t-2xl">
                  <div className="w-full h-56 bg-gradient-to-br from-apple-blue/20 via-apple-blue/10 to-apple-gray-100 dark:from-apple-blue/10 dark:via-dark-surface dark:to-dark-card flex items-center justify-center">
                    <div className="text-apple-blue text-3xl font-bold tracking-wider">
                      {project.title.split(' ').map(word => word[0]).join('')}
                    </div>
                  </div>
                  {/* Replace with actual image */}
                  {/* <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                  /> */}

                  {/* Apple-style Overlay */}
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out flex items-center justify-center gap-6">
                    <motion.a
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-4 bg-white/90 backdrop-blur-sm text-apple-gray-700 rounded-2xl hover:bg-apple-blue hover:text-white transition-all duration-300 shadow-apple"
                    >
                      <FiExternalLink className="w-5 h-5" />
                    </motion.a>
                    <motion.a
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-4 bg-white/90 backdrop-blur-sm text-apple-gray-700 rounded-2xl hover:bg-apple-blue hover:text-white transition-all duration-300 shadow-apple"
                    >
                      <FiGithub className="w-5 h-5" />
                    </motion.a>
                  </div>
                </div>

                {/* Apple-style Project Content */}
                <div className="p-8">
                  <h3 className="text-title mb-3 text-apple-gray-700 dark:text-dark-text">
                    {project.title}
                  </h3>
                  <p className="text-apple-gray-500 dark:text-dark-text-secondary mb-6 leading-relaxed">
                    {project.description}
                  </p>

                  {/* Apple-style Technologies */}
                  <div className="flex flex-wrap gap-2 mb-8">
                    {project.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-3 py-1 bg-apple-gray-100 dark:bg-dark-surface text-apple-gray-600 dark:text-dark-text-secondary text-sm rounded-full font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>

                  {/* Apple-style Action Buttons */}
                  <div className="flex gap-3">
                    <motion.a
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 text-center py-3 px-4 bg-apple-blue text-white rounded-xl hover:bg-apple-blue-dark transition-all duration-200 text-sm font-medium shadow-apple"
                    >
                      View Project
                    </motion.a>
                    <motion.a
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-3 bg-apple-gray-100 dark:bg-dark-surface text-apple-gray-600 dark:text-dark-text-secondary rounded-xl hover:bg-apple-gray-200 dark:hover:bg-dark-card hover:text-apple-blue transition-all duration-200"
                    >
                      <FiGithub className="w-5 h-5" />
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
