import React from 'react';
import { motion } from 'framer-motion';
import { FiDownload, FiCode, FiDatabase, FiGlobe } from 'react-icons/fi';

const About = () => {
  const skills = [
    {
      category: 'Frontend',
      icon: <FiGlobe className="w-6 h-6" />,
      technologies: ['React', 'JavaScript', 'TypeScript', 'Tailwind CSS', 'HTML5', 'CSS3']
    },
    {
      category: 'Backend',
      icon: <FiCode className="w-6 h-6" />,
      technologies: ['Node.js', 'Express.js', 'Python', 'REST APIs', 'GraphQL', 'Microservices']
    },
    {
      category: 'Database & Tools',
      icon: <FiDatabase className="w-6 h-6" />,
      technologies: ['MongoDB', 'PostgreSQL', 'Git', 'Docker', 'AWS', 'Firebase']
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="about" className="section-apple bg-white dark:bg-dark-bg">
      <div className="container-apple">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Apple-style Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-20">
            <h2 className="text-display mb-6">
              About Me
            </h2>
            <p className="text-body max-w-2xl mx-auto">
              Passionate full-stack developer with a love for creating innovative solutions
              and exceptional user experiences.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-16 lg:gap-20 items-center">
            {/* Enhanced Profile Image */}
            <motion.div variants={itemVariants} className="relative order-2 lg:order-1">
              <div className="relative mx-auto lg:mx-0 w-96 h-96 rounded-3xl overflow-hidden shadow-apple-lg">
                <div className="w-full h-full bg-gradient-to-br from-apple-blue/20 via-apple-blue/10 to-apple-gray-100 dark:from-apple-blue/15 dark:via-dark-surface dark:to-dark-card flex items-center justify-center">
                  <div className="text-apple-blue text-8xl font-bold tracking-wider">AT</div>
                </div>
                {/* Replace with actual image */}
                {/* <img
                  src="/api/placeholder/384/384"
                  alt="Abhay Singh Tomar"
                  className="w-full h-full object-cover"
                /> */}
              </div>
              {/* Enhanced Decorative elements */}
              <div className="absolute -top-6 -right-6 w-32 h-32 bg-apple-blue/10 rounded-full opacity-60 animate-float"></div>
              <div className="absolute -bottom-6 -left-6 w-40 h-40 bg-apple-blue/5 rounded-full opacity-40 animate-float" style={{ animationDelay: '1s' }}></div>
            </motion.div>

            {/* Enhanced About Content */}
            <motion.div variants={itemVariants} className="space-y-8 order-1 lg:order-2">
              <h3 className="text-title mb-6 text-apple-gray-700 dark:text-dark-text">
                Hi, I'm Abhay Singh Tomar
              </h3>

              <div className="space-y-6 text-body-sm leading-relaxed">
                <p>
                  I'm a passionate full-stack developer with over 3 years of experience
                  creating web applications that solve real-world problems. I specialize
                  in modern JavaScript frameworks and have a strong foundation in both
                  frontend and backend technologies.
                </p>

                <p>
                  My journey in software development started with a curiosity about how
                  things work on the web. Since then, I've been constantly learning and
                  adapting to new technologies, always striving to write clean, efficient,
                  and maintainable code.
                </p>

                <p>
                  When I'm not coding, you can find me exploring new technologies,
                  contributing to open-source projects, or sharing knowledge with the
                  developer community.
                </p>
              </div>

              {/* Enhanced Resume Download */}
              <motion.a
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                href="/resume.pdf"
                download
                className="inline-flex items-center gap-3 bg-apple-blue text-white font-medium py-4 px-8 rounded-xl hover:bg-apple-blue-dark transition-all duration-300 shadow-apple-lg text-lg"
              >
                <FiDownload className="w-6 h-6" />
                Download Resume
              </motion.a>
            </motion.div>
          </div>

          {/* Enhanced Skills Section */}
          <motion.div variants={itemVariants} className="mt-24">
            <h3 className="text-title text-center mb-16 text-apple-gray-700 dark:text-dark-text">
              Skills & Technologies
            </h3>

            <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
              {skills.map((skill, index) => (
                <motion.div
                  key={skill.category}
                  variants={itemVariants}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="card-apple p-8 lg:p-10"
                >
                  <div className="flex items-center gap-4 mb-6">
                    <div className="p-3 bg-apple-blue/10 text-apple-blue rounded-2xl">
                      {skill.icon}
                    </div>
                    <h4 className="text-xl font-semibold text-apple-gray-700 dark:text-dark-text">
                      {skill.category}
                    </h4>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    {skill.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-4 py-2 bg-apple-gray-100 dark:bg-dark-surface text-apple-gray-600 dark:text-dark-text-secondary text-sm rounded-full hover:bg-apple-blue/10 hover:text-apple-blue transition-all duration-300 border border-apple-gray-200/50 dark:border-apple-gray-600/20"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
