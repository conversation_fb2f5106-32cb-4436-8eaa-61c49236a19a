import React from 'react';
import { motion } from 'framer-motion';
import { FiDownload, FiCode, FiDatabase, FiGlobe } from 'react-icons/fi';

const About = () => {
  const skills = [
    {
      category: 'Frontend',
      icon: <FiGlobe className="w-6 h-6" />,
      technologies: ['React', 'JavaScript', 'TypeScript', 'Tailwind CSS', 'HTML5', 'CSS3']
    },
    {
      category: 'Backend',
      icon: <FiCode className="w-6 h-6" />,
      technologies: ['Node.js', 'Express.js', 'Python', 'REST APIs', 'GraphQL', 'Microservices']
    },
    {
      category: 'Database & Tools',
      icon: <FiDatabase className="w-6 h-6" />,
      technologies: ['MongoDB', 'PostgreSQL', 'Git', 'Docker', 'AWS', 'Firebase']
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="about" className="section-padding bg-gray-50">
      <div className="container-max">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              About Me
            </h2>
            <div className="w-20 h-1 bg-primary-600 mx-auto mb-8"></div>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Passionate full-stack developer with a love for creating innovative solutions 
              and exceptional user experiences.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Profile Image */}
            <motion.div variants={itemVariants} className="relative">
              <div className="relative mx-auto lg:mx-0 w-80 h-80 rounded-2xl overflow-hidden shadow-2xl">
                <div className="w-full h-full bg-gradient-to-br from-primary-400 to-blue-600 flex items-center justify-center">
                  <div className="text-white text-6xl font-bold">AT</div>
                </div>
                {/* Replace with actual image */}
                {/* <img 
                  src="/api/placeholder/320/320" 
                  alt="Abhay Singh Tomar" 
                  className="w-full h-full object-cover"
                /> */}
              </div>
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary-200 rounded-full opacity-60"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-blue-200 rounded-full opacity-40"></div>
            </motion.div>

            {/* About Content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                Hi, I'm Abhay Singh Tomar
              </h3>
              
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  I'm a passionate full-stack developer with over 3 years of experience 
                  creating web applications that solve real-world problems. I specialize 
                  in modern JavaScript frameworks and have a strong foundation in both 
                  frontend and backend technologies.
                </p>
                
                <p>
                  My journey in software development started with a curiosity about how 
                  things work on the web. Since then, I've been constantly learning and 
                  adapting to new technologies, always striving to write clean, efficient, 
                  and maintainable code.
                </p>
                
                <p>
                  When I'm not coding, you can find me exploring new technologies, 
                  contributing to open-source projects, or sharing knowledge with the 
                  developer community.
                </p>
              </div>

              {/* Resume Download */}
              <motion.a
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                href="/resume.pdf"
                download
                className="inline-flex items-center gap-2 bg-primary-600 text-white font-medium py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors duration-200 shadow-lg"
              >
                <FiDownload className="w-5 h-5" />
                Download Resume
              </motion.a>
            </motion.div>
          </div>

          {/* Skills Section */}
          <motion.div variants={itemVariants} className="mt-20">
            <h3 className="text-2xl font-semibold text-gray-900 text-center mb-12">
              Skills & Technologies
            </h3>
            
            <div className="grid md:grid-cols-3 gap-8">
              {skills.map((skill, index) => (
                <motion.div
                  key={skill.category}
                  variants={itemVariants}
                  whileHover={{ y: -5 }}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-primary-100 text-primary-600 rounded-lg">
                      {skill.icon}
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900">
                      {skill.category}
                    </h4>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {skill.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-primary-100 hover:text-primary-700 transition-colors duration-200"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
