# Portfolio Website - <PERSON><PERSON><PERSON>ar

A modern, responsive full-stack portfolio website built with React and Node.js, showcasing professional work and skills with a clean, minimalist design.

## 🚀 Features

### Frontend (React)
- **Modern SPA**: Built with React 18 and Vite for optimal performance
- **Responsive Design**: Mobile-first approach using Tailwind CSS
- **Smooth Animations**: Framer Motion for engaging user interactions
- **SEO Optimized**: Meta tags, Open Graph, and Twitter Card support
- **Accessibility**: WCAG compliant with proper ARIA labels

### Backend (Node.js/Express)
- **RESTful API**: Clean API endpoints for contact form submissions
- **Database Integration**: MongoDB with Mongoose for data persistence
- **Security**: Helmet, CORS, input validation, and rate limiting
- **Error Handling**: Comprehensive error handling and logging

### Key Sections
- **Hero**: Eye-catching introduction with call-to-action
- **About**: Professional bio, skills showcase, and downloadable resume
- **Projects**: Filterable project gallery with live demos and GitHub links
- **Contact**: Functional contact form with backend integration
- **Footer**: Social links and additional contact information

## 🛠️ Tech Stack

### Frontend
- React 18
- Vite
- Tailwind CSS (CDN)
- Framer Motion
- React Router DOM
- React Icons

### Backend
- Node.js
- Express.js
- MongoDB
- Mongoose
- Helmet (Security)
- CORS
- Express Validator
- Dotenv

## 📁 Project Structure

```
abhay_portfolio/
├── client/                 # Frontend React application
│   ├── public/            # Static assets
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── assets/        # Images and other assets
│   │   └── ...
│   ├── package.json
│   └── ...
├── server/                # Backend Node.js application
│   ├── models/           # MongoDB models
│   ├── routes/           # API routes
│   ├── middleware/       # Custom middleware
│   ├── package.json
│   └── ...
└── README.md
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local installation or MongoDB Atlas)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Abhay1930/abhay_portfolio.git
   cd abhay_portfolio
   ```

2. **Setup Backend**
   ```bash
   cd server
   npm install
   cp .env.example .env
   # Edit .env with your MongoDB URI and other configurations
   npm run dev
   ```

3. **Setup Frontend** (in a new terminal)
   ```bash
   cd client
   npm install
   cp .env.example .env
   # Edit .env with your API URL and other configurations
   npm run dev
   ```

4. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000

## ⚙️ Configuration

### Environment Variables

#### Backend (.env)
```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/portfolio
CLIENT_URL=http://localhost:5173
ADMIN_PASSWORD=your_secure_admin_password_here
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:5000
VITE_APP_NAME=Abhay Singh Tomar - Portfolio
VITE_CONTACT_EMAIL=<EMAIL>
VITE_GITHUB_URL=https://github.com/Abhay1930
VITE_LINKEDIN_URL=https://linkedin.com/in/abhay-singh-tomar
```

## 📝 API Endpoints

### Contact Form
- `POST /api/contact` - Submit contact form
- `GET /api/contact` - Get all submissions (admin only)
- `GET /api/health` - Health check

## 🚀 Deployment

### Frontend (Netlify/Vercel)
1. Build the project: `npm run build`
2. Deploy the `dist` folder to your hosting platform
3. Set environment variables in your hosting platform

### Backend (Render/Heroku)
1. Set up MongoDB Atlas for production database
2. Configure environment variables
3. Deploy using Git integration or CLI

### MongoDB Atlas Setup
1. Create a MongoDB Atlas account
2. Create a new cluster
3. Get connection string and update `MONGODB_URI`

## 🎨 Customization

### Content Updates
- Update personal information in components
- Replace placeholder images in `client/public/`
- Update social media links in environment variables
- Replace resume PDF in `client/public/resume.pdf`

### Styling
- Modify Tailwind config in `client/tailwind.config.js`
- Update color scheme and animations
- Customize component styles

### Projects
- Update project data in `client/src/components/Projects.jsx`
- Add project images to `client/src/assets/images/`

## 🧪 Testing

```bash
# Frontend tests
cd client
npm test

# Backend tests
cd server
npm test
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📞 Contact

- **Email**: <EMAIL>
- **LinkedIn**: [Abhay Singh Tomar](https://linkedin.com/in/abhay-singh-tomar)
- **GitHub**: [Abhay1930](https://github.com/Abhay1930)

---

Made with ❤️ and lots of ☕ by Abhay Singh Tomar
