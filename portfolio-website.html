<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Name - Portfolio</title>
    <meta name="description" content="Professional portfolio showcasing web development projects and skills">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- React and ReactDOM CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'bounce-slow': 'bounce 3s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(40px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                    },
                }
            }
        }
    </script>
    
    <style>
        html {
            scroll-behavior: smooth;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
        }
        
        .skill-bar {
            transition: width 2s ease-in-out;
        }
        
        .project-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .project-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 0;
            background-color: #3b82f6;
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        @media (max-width: 768px) {
            .mobile-menu {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .mobile-menu.open {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="font-inter bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // Navigation Component
        const Navigation = () => {
            const [isMenuOpen, setIsMenuOpen] = useState(false);
            const [isScrolled, setIsScrolled] = useState(false);

            useEffect(() => {
                const handleScroll = () => {
                    setIsScrolled(window.scrollY > 50);
                };
                window.addEventListener('scroll', handleScroll);
                return () => window.removeEventListener('scroll', handleScroll);
            }, []);

            const scrollToSection = (sectionId) => {
                const element = document.getElementById(sectionId);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth' });
                    setIsMenuOpen(false);
                }
            };

            return (
                <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
                    isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-transparent'
                }`}>
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between items-center h-16">
                            <div className="flex-shrink-0">
                                <button 
                                    onClick={() => scrollToSection('home')}
                                    className={`text-xl font-bold transition-colors ${
                                        isScrolled ? 'text-gray-900' : 'text-white'
                                    }`}
                                >
                                    Your Name
                                </button>
                            </div>
                            
                            {/* Desktop Menu */}
                            <div className="hidden md:block">
                                <div className="ml-10 flex items-baseline space-x-8">
                                    {['Home', 'About', 'Projects', 'Skills', 'Contact'].map((item) => (
                                        <button
                                            key={item}
                                            onClick={() => scrollToSection(item.toLowerCase())}
                                            className={`nav-link px-3 py-2 text-sm font-medium transition-colors ${
                                                isScrolled ? 'text-gray-700 hover:text-primary-600' : 'text-white hover:text-gray-200'
                                            }`}
                                        >
                                            {item}
                                        </button>
                                    ))}
                                </div>
                            </div>
                            
                            {/* Mobile menu button */}
                            <div className="md:hidden">
                                <button
                                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                                    className={`inline-flex items-center justify-center p-2 rounded-md transition-colors ${
                                        isScrolled ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white/10'
                                    }`}
                                >
                                    <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-lg`}></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    {/* Mobile Menu */}
                    <div className={`md:hidden mobile-menu ${isMenuOpen ? 'open' : ''} fixed top-16 left-0 right-0 bg-white shadow-lg`}>
                        <div className="px-2 pt-2 pb-3 space-y-1">
                            {['Home', 'About', 'Projects', 'Skills', 'Contact'].map((item) => (
                                <button
                                    key={item}
                                    onClick={() => scrollToSection(item.toLowerCase())}
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50 w-full text-left"
                                >
                                    {item}
                                </button>
                            ))}
                        </div>
                    </div>
                </nav>
            );
        };

        // Hero Section Component
        const HeroSection = () => {
            return (
                <section id="home" className="min-h-screen gradient-bg flex items-center justify-center relative overflow-hidden">
                    {/* Background Animation */}
                    <div className="absolute inset-0">
                        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/10 rounded-full blur-3xl animate-bounce-slow"></div>
                        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-bounce-slow" style={{animationDelay: '1s'}}></div>
                    </div>
                    
                    <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
                        <div className="animate-fade-in">
                            <img 
                                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face&auto=format" 
                                alt="Professional headshot" 
                                className="w-32 h-32 rounded-full mx-auto mb-8 border-4 border-white/20 shadow-2xl"
                            />
                            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                                Hi, I'm <span className="text-yellow-300">Your Name</span>
                            </h1>
                            <p className="text-xl md:text-2xl mb-8 text-gray-200 font-light">
                                Full-Stack Developer & Creative Problem Solver
                            </p>
                            <p className="text-lg mb-12 max-w-2xl mx-auto leading-relaxed text-gray-300">
                                I create exceptional digital experiences through innovative web applications, 
                                combining technical expertise with creative design to bring ideas to life.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button 
                                    onClick={() => document.getElementById('projects').scrollIntoView({behavior: 'smooth'})}
                                    className="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
                                >
                                    View My Work
                                </button>
                                <button 
                                    onClick={() => document.getElementById('contact').scrollIntoView({behavior: 'smooth'})}
                                    className="border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 rounded-lg transition-all duration-300"
                                >
                                    Get In Touch
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    {/* Scroll Indicator */}
                    <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                        <i className="fas fa-chevron-down text-white text-2xl"></i>
                    </div>
                </section>
            );
        };

        // About Section Component
        const AboutSection = () => {
            return (
                <section id="about" className="py-20 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16 animate-fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">About Me</h2>
                            <div className="w-20 h-1 bg-primary-600 mx-auto mb-8"></div>
                            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                                Passionate about creating digital solutions that make a difference
                            </p>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="animate-slide-up">
                                <img
                                    src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=600&h=400&fit=crop&auto=format"
                                    alt="Workspace"
                                    className="rounded-2xl shadow-2xl"
                                />
                            </div>

                            <div className="animate-slide-up space-y-6">
                                <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                                    Hello! I'm a passionate developer
                                </h3>
                                <p className="text-gray-600 leading-relaxed">
                                    With over 3 years of experience in web development, I specialize in creating
                                    modern, responsive applications using cutting-edge technologies. My journey
                                    started with a curiosity about how things work on the web, and it has evolved
                                    into a passion for crafting exceptional user experiences.
                                </p>
                                <p className="text-gray-600 leading-relaxed">
                                    I believe in writing clean, maintainable code and staying up-to-date with
                                    the latest industry trends. When I'm not coding, you can find me exploring
                                    new technologies, contributing to open-source projects, or sharing knowledge
                                    with the developer community.
                                </p>

                                <div className="pt-6">
                                    <a
                                        href="#"
                                        className="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105"
                                        download
                                    >
                                        <i className="fas fa-download mr-2"></i>
                                        Download Resume
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Projects Section Component
        const ProjectsSection = () => {
            const projects = [
                {
                    id: 1,
                    title: "E-Commerce Platform",
                    description: "A full-stack e-commerce solution with user authentication, payment integration, and admin dashboard.",
                    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop&auto=format",
                    technologies: ["React", "Node.js", "MongoDB", "Stripe"],
                    liveUrl: "#",
                    githubUrl: "#"
                },
                {
                    id: 2,
                    title: "Task Management App",
                    description: "A collaborative task management application with real-time updates and team collaboration features.",
                    image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=250&fit=crop&auto=format",
                    technologies: ["React", "Firebase", "Material-UI", "Socket.io"],
                    liveUrl: "#",
                    githubUrl: "#"
                },
                {
                    id: 3,
                    title: "Weather Dashboard",
                    description: "A responsive weather application with location-based forecasts and interactive charts.",
                    image: "https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=400&h=250&fit=crop&auto=format",
                    technologies: ["React", "Chart.js", "OpenWeather API", "Tailwind CSS"],
                    liveUrl: "#",
                    githubUrl: "#"
                },
                {
                    id: 4,
                    title: "Portfolio Website",
                    description: "A modern, responsive portfolio website showcasing projects and skills with smooth animations.",
                    image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=400&h=250&fit=crop&auto=format",
                    technologies: ["React", "Tailwind CSS", "Framer Motion", "Netlify"],
                    liveUrl: "#",
                    githubUrl: "#"
                }
            ];

            return (
                <section id="projects" className="py-20 bg-gray-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16 animate-fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Projects</h2>
                            <div className="w-20 h-1 bg-primary-600 mx-auto mb-8"></div>
                            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                                Here are some of my recent projects that showcase my skills and passion for development
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
                            {projects.map((project) => (
                                <div key={project.id} className="project-card bg-white rounded-2xl shadow-lg overflow-hidden">
                                    <div className="relative overflow-hidden">
                                        <img
                                            src={project.image}
                                            alt={project.title}
                                            className="w-full h-48 object-cover transition-transform duration-300 hover:scale-110"
                                        />
                                        <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-4">
                                            <a
                                                href={project.liveUrl}
                                                className="bg-white text-gray-900 p-3 rounded-full hover:bg-primary-600 hover:text-white transition-colors duration-300"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                <i className="fas fa-external-link-alt"></i>
                                            </a>
                                            <a
                                                href={project.githubUrl}
                                                className="bg-white text-gray-900 p-3 rounded-full hover:bg-primary-600 hover:text-white transition-colors duration-300"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                <i className="fab fa-github"></i>
                                            </a>
                                        </div>
                                    </div>

                                    <div className="p-6">
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">{project.title}</h3>
                                        <p className="text-gray-600 mb-4 leading-relaxed">{project.description}</p>

                                        <div className="flex flex-wrap gap-2 mb-4">
                                            {project.technologies.map((tech) => (
                                                <span
                                                    key={tech}
                                                    className="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full"
                                                >
                                                    {tech}
                                                </span>
                                            ))}
                                        </div>

                                        <div className="flex gap-3">
                                            <a
                                                href={project.liveUrl}
                                                className="flex-1 text-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-300"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                View Project
                                            </a>
                                            <a
                                                href={project.githubUrl}
                                                className="p-2 border border-gray-300 text-gray-600 rounded-lg hover:border-primary-600 hover:text-primary-600 transition-colors duration-300"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                <i className="fab fa-github text-lg"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>
            );
        };

        // Skills Section Component
        const SkillsSection = () => {
            const [isVisible, setIsVisible] = useState(false);
            const skillsRef = useRef(null);

            useEffect(() => {
                const observer = new IntersectionObserver(
                    ([entry]) => {
                        if (entry.isIntersecting) {
                            setIsVisible(true);
                        }
                    },
                    { threshold: 0.3 }
                );

                if (skillsRef.current) {
                    observer.observe(skillsRef.current);
                }

                return () => observer.disconnect();
            }, []);

            const skills = [
                { name: "JavaScript", level: 90, icon: "fab fa-js-square" },
                { name: "React", level: 85, icon: "fab fa-react" },
                { name: "Node.js", level: 80, icon: "fab fa-node-js" },
                { name: "Python", level: 75, icon: "fab fa-python" },
                { name: "HTML/CSS", level: 95, icon: "fab fa-html5" },
                { name: "MongoDB", level: 70, icon: "fas fa-database" },
                { name: "Git", level: 85, icon: "fab fa-git-alt" },
                { name: "AWS", level: 65, icon: "fab fa-aws" }
            ];

            const softSkills = [
                "Problem Solving",
                "Team Collaboration",
                "Communication",
                "Project Management",
                "Creative Thinking",
                "Adaptability"
            ];

            return (
                <section id="skills" className="py-20 bg-white" ref={skillsRef}>
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16 animate-fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Skills & Expertise</h2>
                            <div className="w-20 h-1 bg-primary-600 mx-auto mb-8"></div>
                            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                                Technologies and tools I work with to bring ideas to life
                            </p>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-12">
                            {/* Technical Skills */}
                            <div className="animate-slide-up">
                                <h3 className="text-2xl font-semibold text-gray-900 mb-8">Technical Skills</h3>
                                <div className="space-y-6">
                                    {skills.map((skill) => (
                                        <div key={skill.name} className="flex items-center">
                                            <div className="flex items-center w-32">
                                                <i className={`${skill.icon} text-2xl text-primary-600 mr-3`}></i>
                                                <span className="font-medium text-gray-700">{skill.name}</span>
                                            </div>
                                            <div className="flex-1 ml-4">
                                                <div className="bg-gray-200 rounded-full h-3">
                                                    <div
                                                        className={`skill-bar bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full ${isVisible ? '' : 'w-0'}`}
                                                        style={{ width: isVisible ? `${skill.level}%` : '0%' }}
                                                    ></div>
                                                </div>
                                                <span className="text-sm text-gray-500 mt-1 block">{skill.level}%</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Soft Skills */}
                            <div className="animate-slide-up">
                                <h3 className="text-2xl font-semibold text-gray-900 mb-8">Soft Skills</h3>
                                <div className="grid grid-cols-2 gap-4">
                                    {softSkills.map((skill, index) => (
                                        <div
                                            key={skill}
                                            className="bg-gradient-to-r from-primary-50 to-primary-100 p-4 rounded-lg text-center hover:shadow-md transition-shadow duration-300"
                                            style={{ animationDelay: `${index * 0.1}s` }}
                                        >
                                            <span className="text-primary-700 font-medium">{skill}</span>
                                        </div>
                                    ))}
                                </div>

                                <div className="mt-8 p-6 bg-gray-50 rounded-xl">
                                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Certifications & Learning</h4>
                                    <ul className="space-y-2 text-gray-600">
                                        <li className="flex items-center">
                                            <i className="fas fa-certificate text-primary-600 mr-2"></i>
                                            AWS Certified Developer (In Progress)
                                        </li>
                                        <li className="flex items-center">
                                            <i className="fas fa-certificate text-primary-600 mr-2"></i>
                                            React Developer Certification
                                        </li>
                                        <li className="flex items-center">
                                            <i className="fas fa-certificate text-primary-600 mr-2"></i>
                                            JavaScript Algorithms and Data Structures
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Contact Section Component
        const ContactSection = () => {
            const [formData, setFormData] = useState({
                name: '',
                email: '',
                message: ''
            });

            const handleInputChange = (e) => {
                const { name, value } = e.target;
                setFormData(prev => ({
                    ...prev,
                    [name]: value
                }));
            };

            const handleSubmit = (e) => {
                e.preventDefault();
                // Form submission would be handled here
                alert('Thank you for your message! I\'ll get back to you soon.');
                setFormData({ name: '', email: '', message: '' });
            };

            return (
                <section id="contact" className="py-20 bg-gray-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16 animate-fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Get In Touch</h2>
                            <div className="w-20 h-1 bg-primary-600 mx-auto mb-8"></div>
                            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                                Have a project in mind or want to collaborate? I'd love to hear from you!
                            </p>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-12">
                            {/* Contact Information */}
                            <div className="animate-slide-up">
                                <h3 className="text-2xl font-semibold text-gray-900 mb-8">Let's Connect</h3>
                                <div className="space-y-6">
                                    <div className="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <div className="bg-primary-100 p-3 rounded-lg mr-4">
                                            <i className="fas fa-envelope text-primary-600 text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900">Email</h4>
                                            <p className="text-gray-600"><EMAIL></p>
                                        </div>
                                    </div>

                                    <div className="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <div className="bg-primary-100 p-3 rounded-lg mr-4">
                                            <i className="fas fa-phone text-primary-600 text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900">Phone</h4>
                                            <p className="text-gray-600">+****************</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <div className="bg-primary-100 p-3 rounded-lg mr-4">
                                            <i className="fas fa-map-marker-alt text-primary-600 text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900">Location</h4>
                                            <p className="text-gray-600">Your City, Country</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Social Media Links */}
                                <div className="mt-8">
                                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Follow Me</h4>
                                    <div className="flex space-x-4">
                                        <a
                                            href="#"
                                            className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md hover:bg-primary-600 hover:text-white transition-all duration-300 text-gray-600"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <i className="fab fa-linkedin text-xl"></i>
                                        </a>
                                        <a
                                            href="#"
                                            className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md hover:bg-primary-600 hover:text-white transition-all duration-300 text-gray-600"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <i className="fab fa-github text-xl"></i>
                                        </a>
                                        <a
                                            href="#"
                                            className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md hover:bg-primary-600 hover:text-white transition-all duration-300 text-gray-600"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <i className="fab fa-twitter text-xl"></i>
                                        </a>
                                        <a
                                            href="#"
                                            className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md hover:bg-primary-600 hover:text-white transition-all duration-300 text-gray-600"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <i className="fab fa-instagram text-xl"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            {/* Contact Form */}
                            <div className="animate-slide-up">
                                <div className="bg-white p-8 rounded-2xl shadow-lg">
                                    <h3 className="text-2xl font-semibold text-gray-900 mb-6">Send Message</h3>
                                    <div className="space-y-6">
                                        <div>
                                            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                                                Name *
                                            </label>
                                            <input
                                                type="text"
                                                id="name"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                required
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                                                placeholder="Your full name"
                                            />
                                        </div>

                                        <div>
                                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                                Email *
                                            </label>
                                            <input
                                                type="email"
                                                id="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={handleInputChange}
                                                required
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                                                placeholder="<EMAIL>"
                                            />
                                        </div>

                                        <div>
                                            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                                                Message *
                                            </label>
                                            <textarea
                                                id="message"
                                                name="message"
                                                value={formData.message}
                                                onChange={handleInputChange}
                                                required
                                                rows={5}
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 resize-none"
                                                placeholder="Tell me about your project or just say hello..."
                                            />
                                        </div>

                                        <button
                                            onClick={handleSubmit}
                                            className="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
                                        >
                                            <i className="fas fa-paper-plane mr-2"></i>
                                            Send Message
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Footer Component
        const Footer = () => {
            const currentYear = new Date().getFullYear();

            const scrollToSection = (sectionId) => {
                const element = document.getElementById(sectionId);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth' });
                }
            };

            const scrollToTop = () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            };

            return (
                <footer className="bg-gray-900 text-white py-8">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        {/* Main Footer Content */}
                        <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
                            {/* Brand & Social */}
                            <div className="text-center md:text-left">
                                <h3 className="text-xl font-bold mb-3">Your Name</h3>
                                <p className="text-gray-400 text-sm mb-4 max-w-xs">
                                    Full-Stack Developer crafting digital experiences
                                </p>
                                <div className="flex justify-center md:justify-start space-x-4">
                                    <a
                                        href="#"
                                        className="text-gray-400 hover:text-primary-400 transition-colors duration-300 transform hover:scale-110"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        aria-label="LinkedIn"
                                    >
                                        <i className="fab fa-linkedin text-xl"></i>
                                    </a>
                                    <a
                                        href="#"
                                        className="text-gray-400 hover:text-primary-400 transition-colors duration-300 transform hover:scale-110"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        aria-label="GitHub"
                                    >
                                        <i className="fab fa-github text-xl"></i>
                                    </a>
                                    <a
                                        href="#"
                                        className="text-gray-400 hover:text-primary-400 transition-colors duration-300 transform hover:scale-110"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        aria-label="Twitter"
                                    >
                                        <i className="fab fa-twitter text-xl"></i>
                                    </a>
                                    <a
                                        href="#"
                                        className="text-gray-400 hover:text-primary-400 transition-colors duration-300 transform hover:scale-110"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        aria-label="Email"
                                    >
                                        <i className="fas fa-envelope text-xl"></i>
                                    </a>
                                </div>
                            </div>

                            {/* Quick Navigation */}
                            <div className="flex flex-wrap justify-center gap-6 text-sm">
                                {['Home', 'About', 'Projects', 'Skills', 'Contact'].map((item) => (
                                    <button
                                        key={item}
                                        onClick={() => scrollToSection(item.toLowerCase())}
                                        className="text-gray-400 hover:text-white transition-colors duration-300 hover:underline"
                                    >
                                        {item}
                                    </button>
                                ))}
                            </div>

                            {/* Back to Top */}
                            <button
                                onClick={scrollToTop}
                                className="bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full transition-all duration-300 transform hover:scale-110 shadow-lg"
                                aria-label="Back to top"
                            >
                                <i className="fas fa-arrow-up"></i>
                            </button>
                        </div>

                        {/* Copyright */}
                        <div className="border-t border-gray-800 mt-6 pt-6 text-center">
                            <p className="text-gray-400 text-sm flex items-center justify-center">
                                © {currentYear} Your Name. Made with
                                <i className="fas fa-heart text-red-500 mx-1 animate-pulse"></i>
                                and lots of ☕
                            </p>
                        </div>
                    </div>
                </footer>
            );
        };

        // Main App Component
        const App = () => {
            return (
                <div className="min-h-screen">
                    <Navigation />
                    <HeroSection />
                    <AboutSection />
                    <ProjectsSection />
                    <SkillsSection />
                    <ContactSection />
                    <Footer />
                </div>
            );
        };

        // Render the App
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
